'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  Typography,
  Container,
  Paper,
  Button,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  Alert,
  Card,
  CardContent,
  Divider,
  CircularProgress,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  AppBar,
  Toolbar,
  IconButton,
  CssBaseline,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Send as SendIcon,
  Menu as MenuIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as RadioButtonUncheckedIcon,
  ArrowForward as ArrowForwardIcon,
  Home as HomeIcon,
} from '@mui/icons-material';
import { ISurveyVm } from '../../../lib/api/surveys';
import { useSurveyQuestions } from '../../_constants/survey-questions';
import { useTranslation } from '../../_components/translation-provider';
import DarkModeToggle from '../../_components/dark-mode-toggle';
import LanguageSelector from '../../_components/language-selector';
import LanguageSelectorCompact from '../../_components/language-selector-compact';
import { useTheme as useCustomTheme } from '../../_components/theme-provider';
import { ElevatedFormControl } from '../../_components/ElevatedFormControl';
import CustomThemeProvider from '../../_components/theme-provider';

interface FormData {
  [key: string]: string;
}

const drawerWidth = 280;

function SurveyContent({ surveyVm }: { surveyVm: ISurveyVm }) {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [formData, setFormData] = useState<FormData>({});
  const [formError, setFormError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [lockedQuestions, setLockedQuestions] = useState<{
    [key: number]: boolean;
  }>({});
  const [isLoadingExisting, setIsLoadingExisting] = useState(true);
  const surveyQuestions = useSurveyQuestions();
  const { t } = useTranslation();
  const customTheme = useCustomTheme();

  // Load existing answers on mount
  useEffect(() => {
    const loadExistingAnswers = async () => {
      setIsLoadingExisting(true);
      try {
        const response = await fetch(
          `/api/responses/get-progress?surveyId=${
            surveyVm.id
          }&userEmail=${encodeURIComponent(surveyVm.userEmail)}`
        );

        if (response.ok) {
          const data = await response.json();

          if (data.answers && Object.keys(data.answers).length > 0) {
            setFormData(data.answers);

            // Lock questions that have been answered
            const locked: { [key: number]: boolean } = {};
            let firstUnansweredQuestion = 0;
            let foundUnanswered = false;

            for (let i = 0; i < surveyQuestions.length; i++) {
              const questionKey = `q${i + 1}`;
              const commentKey = `q${i + 1}-comment`;
              if (data.answers[questionKey] && data.answers[commentKey]) {
                locked[i] = true;
              } else if (!foundUnanswered) {
                firstUnansweredQuestion = i;
                foundUnanswered = true;
              }
            }
            setLockedQuestions(locked);

            // Navigate to the first unanswered question, or stay at 0 if all are answered
            setCurrentQuestion(firstUnansweredQuestion);

            // If survey is complete, mark as submitted
            if (data.isComplete) {
              setSubmitted(true);
            }
          }
        }
      } catch (error) {
        console.error('Error loading existing answers:', error);
      } finally {
        setIsLoadingExisting(false);
      }
    };
    loadExistingAnswers();
  }, [surveyVm.id, surveyVm.userEmail, surveyQuestions.length]);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleRadioChange = (questionIndex: number, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [`q${questionIndex + 1}`]: value,
    }));
    setFormError(null);
  };

  const handleCommentChange = (questionIndex: number, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [`q${questionIndex + 1}-comment`]: value,
    }));
    setFormError(null);
  };

  const validateCurrentQuestion = (): boolean => {
    const radioValue = formData[`q${currentQuestion + 1}`];
    const commentValue = formData[`q${currentQuestion + 1}-comment`];

    if (!radioValue) {
      setFormError(
        `Please select a rating for question ${currentQuestion + 1}.`
      );
      return false;
    }

    if (!commentValue || commentValue.trim() === '') {
      setFormError(
        `Please provide feedback for question ${currentQuestion + 1}.`
      );
      return false;
    }

    setFormError(null);
    return true;
  };

  const handleNextQuestion = async () => {
    if (!validateCurrentQuestion()) {
      return;
    }
    const questionId = `q${currentQuestion + 1}`;
    const value = formData[questionId];
    const comment = formData[`${questionId}-comment`];
    try {
      const res = await fetch('/api/responses/save-progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          surveyId: surveyVm.id,
          waveId: surveyVm.waveId,
          questionId,
          value,
          comment,
          userEmail: surveyVm.userEmail,
          userName: surveyVm.userName,
        }),
      });
      if (res.status === 409) {
        const errorData = await res.json();
        setFormError(
          errorData.error ||
            'You have already submitted an answer for this question.'
        );
        setLockedQuestions((prev) => ({ ...prev, [currentQuestion]: true }));
        return;
      }
      if (!res.ok) {
        const errorData = await res.json();
        setFormError(
          errorData.error || 'Failed to save your answer. Please try again.'
        );

        // Log detailed error information for debugging
        if (errorData.details) {
          console.error('Save progress error details:', errorData);
        }
        return;
      }
      // Scroll to top
      window.scrollTo({ top: 0, behavior: 'smooth' });
      // Move to next question
      if (currentQuestion < surveyQuestions.length - 1) {
        setCurrentQuestion(currentQuestion + 1);
      } else {
        handleSubmit();
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (_) {
      setFormError(
        'Network error: Unable to save your answer. Please check your internet connection and try again. If the problem persists, contact <NAME_EMAIL> with error code: NETWORK_ERROR'
      );
    }
  };

  const handleQuestionNavigation = (questionIndex: number) => {
    // Allow navigation to:
    // 1. Any answered/locked question (for review)
    // 2. Any unanswered question (for completion)
    const canNavigate = true; // Allow navigation to any question

    if (canNavigate) {
      setCurrentQuestion(questionIndex);
      setFormError(null);
      if (isMobile) {
        setMobileOpen(false);
      }
    }
  };

  const handleSubmit = async () => {
    setFormError(null);

    if (!validateCurrentQuestion()) {
      return;
    }

    // Validate all questions
    for (let i = 0; i < surveyQuestions.length; i++) {
      const radioValue = formData[`q${i + 1}`];
      const commentValue = formData[`q${i + 1}-comment`];

      if (!radioValue) {
        setFormError(`Please select a rating for question ${i + 1}.`);
        return;
      }

      if (!commentValue || commentValue.trim() === '') {
        setFormError(`Please provide feedback for question ${i + 1}.`);
        return;
      }
    }

    setIsSubmitting(true);

    try {
      // Map the form data into the submission format
      const answers = surveyQuestions.map((question, i) => ({
        questionId: `q${i + 1}`,
        value: formData[`q${i + 1}`],
        comment: formData[`q${i + 1}-comment`],
      }));

      const submissionData = {
        surveyId: surveyVm.id,
        waveId: surveyVm.waveId,
        answers,
        userEmail: surveyVm.userEmail,
        userName: surveyVm.userName,
      };

      // TODO: Replace with actual API call
      console.log('Submitting survey:', submissionData);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      setSubmitted(true);
    } catch (error) {
      console.error('Error submitting survey:', error);
      setFormError('Failed to submit survey. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBackToDashboard = () => {
    router.push('/dashboard');
  };

  const getAnswerStatus = (
    questionIndex: number
  ): 'answered' | 'unanswered' => {
    const radioValue = formData[`q${questionIndex + 1}`];
    const commentValue = formData[`q${questionIndex + 1}-comment`];

    return radioValue && commentValue && commentValue.trim() !== ''
      ? 'answered'
      : 'unanswered';
  };

  const getProgressPercentage = () => {
    const answeredCount = surveyQuestions
      .map((_, index) => getAnswerStatus(index))
      .filter((status) => status === 'answered').length;
    return (answeredCount / surveyQuestions.length) * 100;
  };

  if (submitted) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h4" color="success.main" gutterBottom>
            Thank You!
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Your survey has been submitted successfully.
          </Typography>
          <Button
            variant="contained"
            onClick={handleBackToDashboard}
            startIcon={<HomeIcon />}
          >
            Back to Dashboard
          </Button>
        </Paper>
      </Container>
    );
  }

  // Show loading state while fetching existing answers
  if (isLoadingExisting) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
          flexDirection: 'column',
          gap: 2,
        }}
      >
        <CircularProgress size={48} />
        <Typography variant="h6" color="text.secondary">
          {t('survey.loading')}
        </Typography>
      </Box>
    );
  }

  const currentQuestionData = surveyQuestions[currentQuestion];
  const progressPercentage = getProgressPercentage();

  const drawer = (
    <>
      <Toolbar sx={{ minHeight: 64 }}>
        <Typography variant="h6">Survey Progress</Typography>
      </Toolbar>
      <Divider />

      {/* Progress Indicator */}
      <Box sx={{ p: 2 }}>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Progress: {Math.round(progressPercentage)}%
        </Typography>
        <Box
          sx={{
            width: '100%',
            bgcolor: 'grey.200',
            borderRadius: 1,
            height: 8,
            mb: 2,
          }}
        >
          <Box
            sx={{
              width: `${progressPercentage}%`,
              bgcolor: 'primary.main',
              height: '100%',
              borderRadius: 1,
              transition: 'width 0.3s ease',
            }}
          />
        </Box>
      </Box>

      <Divider />

      {/* Questions Navigation */}
      <List>
        {surveyQuestions.map((question, index) => {
          const status = getAnswerStatus(index);
          const isCurrent = index === currentQuestion;
          const isAnswered = status === 'answered';
          const canNavigate = true; // All questions are now navigable

          return (
            <ListItem key={index} disablePadding>
              <ListItemButton
                onClick={() => handleQuestionNavigation(index)}
                disabled={!canNavigate}
                selected={isCurrent}
                sx={{
                  opacity: 1,
                  '&.Mui-selected': {
                    bgcolor: 'primary.light',
                    '&:hover': {
                      bgcolor: 'primary.light',
                    },
                  },
                }}
              >
                <ListItemIcon>
                  {isAnswered ? (
                    <CheckCircleIcon color="success" />
                  ) : (
                    <RadioButtonUncheckedIcon color="action" />
                  )}
                </ListItemIcon>
                <ListItemText
                  primary={`${t('survey.question')} ${index + 1}`}
                  secondary={question.name}
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isCurrent ? 600 : 400,
                  }}
                  secondaryTypographyProps={{
                    fontSize: '0.75rem',
                    color: isCurrent ? 'primary.main' : 'text.secondary',
                  }}
                />
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>

      <Box sx={{ flexGrow: 1 }} />
      <Divider />

      {/* Settings */}
      <List sx={{ mb: 1 }}>
        <ListItem disablePadding>
          <ListItemButton onClick={customTheme.toggleDarkMode}>
            <ListItemIcon>
              <DarkModeToggle />
            </ListItemIcon>
            <ListItemText primary={t('dashboard.darkMode')} />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding>
          <LanguageSelector />
        </ListItem>
      </List>
      <Divider />

      {/* Navigation Actions */}
      <List>
        <ListItem disablePadding>
          <ListItemButton onClick={handleBackToDashboard}>
            <ListItemIcon>
              <HomeIcon color="action" />
            </ListItemIcon>
            <ListItemText primary={t('survey.backToDashboard')} />
          </ListItemButton>
        </ListItem>
      </List>
    </>
  );

  return (
    <Box sx={{ display: 'flex', bgcolor: 'grey.100', minHeight: '100vh' }}>
      <CssBaseline />

      {/* Mobile Drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
            bgcolor: 'background.paper',
          },
        }}
      >
        {drawer}
      </Drawer>

      {/* Desktop Drawer */}
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', md: 'block' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
            bgcolor: 'background.paper',
            borderRight: 0,
          },
        }}
        open
      >
        {drawer}
      </Drawer>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 0,
          width: { xs: '100%', md: `calc(100% - ${drawerWidth}px)` },
          ml: { xs: 0, md: `${drawerWidth}px` },
          minHeight: '100vh',
          bgcolor: 'grey.100',
        }}
      >
        {/* Top Bar */}
        <AppBar
          position="fixed"
          color="inherit"
          elevation={0}
          sx={{
            width: { xs: '100%', md: `calc(100% - ${drawerWidth}px)` },
            ml: { xs: 0, md: `${drawerWidth}px` },
            borderBottom: 1,
            borderColor: 'divider',
            bgcolor: 'background.paper',
          }}
        >
          <Toolbar sx={{ justifyContent: 'space-between', minHeight: 64 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={handleDrawerToggle}
                sx={{ mr: 2, display: { md: 'none' } }}
              >
                <MenuIcon />
              </IconButton>
              <Typography
                variant="h6"
                sx={{ display: { xs: 'none', sm: 'block' } }}
              >
                {t('survey.questionXofY', {
                  current: currentQuestion + 1,
                  total: surveyQuestions.length,
                })}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box
                sx={{
                  display: { xs: 'none', md: 'flex' },
                  alignItems: 'center',
                  gap: 0.5,
                }}
              >
                <DarkModeToggle />
                <LanguageSelectorCompact />
              </Box>
              <Typography variant="body1" color="text.secondary">
                {surveyVm.userName}
              </Typography>
            </Box>
          </Toolbar>
        </AppBar>
        <Toolbar sx={{ minHeight: 64 }} />

        <Container maxWidth="md" sx={{ mt: 2, mb: 4, px: { xs: 2, sm: 3 } }}>
          <Paper sx={{ overflow: 'hidden' }}>
            {/* Header Section */}
            <Box sx={{ bgcolor: 'primary.main', height: 8 }} />

            <Box sx={{ py: 2, px: 4 }}>
              <Typography variant="h6" color="text.secondary">
                {surveyVm.assessmentType === 'Anheuser Busch In-Bev-on-Agency'
                  ? 'ABI on Agency'
                  : 'Agency on ABI'}
              </Typography>

              <Typography variant="h6" gutterBottom>
                <strong>{surveyVm.agencyName}</strong> • {surveyVm.agencyType}
                <br />
                {surveyVm.country}
              </Typography>
            </Box>

            <Divider />

            {/* Question Section */}
            <Card
              sx={{
                p: { xs: 2, sm: 3 },
                bgcolor: 'background.paper',
                boxShadow: 0,
              }}
            >
              <CardContent
                sx={{
                  pt: 0,
                }}
              >
                <Typography variant="h6" fontWeight={600} gutterBottom>
                  {currentQuestion + 1} - {currentQuestionData.name}
                </Typography>
                <Typography variant="body1" color="text.secondary" paragraph>
                  {currentQuestionData.text}
                </Typography>

                {/* Rating Scale */}
                <Box sx={{ my: 3, width: '100%' }}>
                  <RadioGroup
                    row
                    value={formData[`q${currentQuestion + 1}`] || ''}
                    onChange={(e) =>
                      handleRadioChange(currentQuestion, e.target.value)
                    }
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      width: '100%',
                    }}
                  >
                    {[1, 2, 3, 4, 5].map((value) => (
                      <Box key={value} sx={{ textAlign: 'center', flex: 1 }}>
                        <FormControlLabel
                          value={value.toString()}
                          control={<Radio size="medium" sx={{ p: 0.5 }} />}
                          label={
                            <Typography variant="body2" sx={{ mt: 0.5 }}>
                              {value}
                            </Typography>
                          }
                          labelPlacement="bottom"
                          sx={{ m: 0, minWidth: 32 }}
                          disabled={lockedQuestions[currentQuestion]}
                        />
                      </Box>
                    ))}
                  </RadioGroup>
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      mt: 1,
                    }}
                  >
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ textAlign: 'left', minWidth: 80 }}
                    >
                      {t('survey.disagreeCompletely')}
                    </Typography>
                    <Box sx={{ flex: 1 }} />
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ textAlign: 'right', minWidth: 80 }}
                    >
                      {t('survey.agreeCompletely')}
                    </Typography>
                  </Box>
                </Box>

                {/* Comment Field */}
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    {t('survey.feedback')}
                  </Typography>
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    variant="outlined"
                    placeholder={t('survey.feedbackPrompt')}
                    value={formData[`q${currentQuestion + 1}-comment`] || ''}
                    onChange={(e) =>
                      handleCommentChange(currentQuestion, e.target.value)
                    }
                    name={`q${currentQuestion + 1}-comment`}
                    disabled={lockedQuestions[currentQuestion]}
                  />
                </Box>
              </CardContent>
            </Card>

            <Divider />

            {/* Error Message */}
            {formError && (
              <Alert severity="error" sx={{ mt: 3 }}>
                {formError}
              </Alert>
            )}

            {/* Action Buttons */}
            <Box
              sx={{
                display: 'flex',
                gap: 2,
                p: 2,
                alignItems: 'center',
                justifyContent: 'center',
                flexWrap: 'wrap',
              }}
            >
              {/* Main Action Button */}
              <ElevatedFormControl>
                <Button
                  variant="contained"
                  onClick={handleNextQuestion}
                  disabled={isSubmitting || lockedQuestions[currentQuestion]}
                  endIcon={
                    isSubmitting ? (
                      <CircularProgress size={20} />
                    ) : currentQuestion === surveyQuestions.length - 1 ? (
                      <SendIcon />
                    ) : lockedQuestions[currentQuestion] ? (
                      <CheckCircleIcon />
                    ) : (
                      <ArrowForwardIcon />
                    )
                  }
                  sx={{ minWidth: 140 }}
                >
                  {lockedQuestions[currentQuestion]
                    ? t('survey.alreadyAnswered')
                    : isSubmitting
                    ? t('survey.submitting')
                    : currentQuestion === surveyQuestions.length - 1
                    ? t('survey.submitSurvey')
                    : t('survey.saveAndContinue')}
                </Button>
              </ElevatedFormControl>
            </Box>
          </Paper>
        </Container>
      </Box>
    </Box>
  );
}

export default function SurveyClient({ surveyVm }: { surveyVm: ISurveyVm }) {
  return (
    <CustomThemeProvider>
      <SurveyContent surveyVm={surveyVm} />
    </CustomThemeProvider>
  );
}
