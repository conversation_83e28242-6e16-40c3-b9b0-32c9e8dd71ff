import { Language } from '../_components/translation-provider';

export interface Translation {
  [key: string]: string;
}

export interface Translations {
  en: Translation;
  es: Translation;
  pt: Translation;
}

export const translations: Translations = {
  en: {
    // Dashboard
    'dashboard.title': 'Your Surveys',
    'dashboard.surveysCount': 'You have {count} survey{plural} to complete.',
    'dashboard.noSurveys': 'No surveys available at this time.',
    'dashboard.startSurvey': 'Start Survey',
    'dashboard.continueSurvey': 'Continue Survey',
    'dashboard.completed': 'Completed',
    'dashboard.yourPasscode': 'Your Passcode:',
    'dashboard.email': 'Email:',
    'dashboard.darkMode': 'Dark Mode',
    'dashboard.language': 'Language',
    'dashboard.logout': 'Logout',
    'dashboard.welcome': 'Welcome,',
    'dashboard.pending': 'Pending',
    'dashboard.inProgress': 'In Progress',
    'dashboard.assessment': 'Assessment',
    'dashboard.agencyAssessmentOfABI': 'Agency Assessment of ABI',
    'dashboard.abiAssessmentOfAgency': 'ABI Assessment of Agency',
    
    // Survey Questions
    'question.1.name': 'People and Partnership',
    'question.1.text': "Agency allocates talented teams according to the client's requirements, fosters partnership with other agencies, makes client aware of possible issues and treats stakeholders with responsibility and respect.",
    'question.2.name': 'Account Management and Process',
    'question.2.text': 'Agency ensures projects are delivered on time, on brief and on budget, including updated timelines, cost transparency and active budget optimization',
    'question.3.name': 'Strategy',
    'question.3.text': 'Agency understands our business, industry and customers and provides strategies that work for our brands; best practices from other categories are regularly shared and lessons learned from the past are translated to future strategic thinking',
    'question.4.name': 'Creativity',
    'question.4.text': 'Agency delivers ideas on-brief and executable, effectively translating ideas into in-market results; agency elevates the level of creativity with bold creative solutions and proactively brings new opportunities to our team.',
    'question.5.name': 'Media Planning and Execution',
    'question.5.text': 'Agency understands media performance metrics and uses analytics to drive results; agency demonstrates digital focus on implementation beyond traditional advertising and considers main consumer trends; incorporates a test-and-learn mindset, moving with agility to optimize executions with real-time in-market learning.',
    
    // Survey Interface
    'survey.question': 'Question',
    'survey.next': 'Next',
    'survey.previous': 'Previous',
    'survey.submit': 'Submit',
    'survey.save': 'Save Progress',
    'survey.comment': 'Comment',
    'survey.rating': 'Rating',
    'survey.progress': 'Progress',
    'survey.loading': 'Loading survey...',
    'survey.submitSurvey': 'Submit Survey',
    'survey.saveAndContinue': 'Next Question',
    'survey.alreadyAnswered': 'Already Answered',
    'survey.submitting': 'Submitting...',
    'survey.questionXofY': 'Question {current} of {total}',
    'survey.disagreeCompletely': 'Disagree Completely',
    'survey.agreeCompletely': 'Agree Completely',
    'survey.feedback': 'Please tell us what is working, and identify any opportunities for improvements:',
    'survey.feedbackPrompt': 'Please provide feedback outlining your experience with this agency on the above topic. Your feedback should be specific and actionable.',
    'survey.backToDashboard': 'Back to Dashboard',
    
    // Languages
    'language.english': 'English',
    'language.spanish': 'Español',
    'language.portuguese': 'Português',
  },
  es: {
    // Dashboard - Placeholders for Spanish translations
    'dashboard.title': '[ES] Your Surveys',
    'dashboard.surveysCount': '[ES] You have {count} survey{plural} to complete.',
    'dashboard.noSurveys': '[ES] No surveys available at this time.',
    'dashboard.startSurvey': '[ES] Start Survey',
    'dashboard.continueSurvey': '[ES] Continue Survey',
    'dashboard.completed': '[ES] Completed',
    'dashboard.yourPasscode': '[ES] Your Passcode:',
    'dashboard.email': '[ES] Email:',
    'dashboard.darkMode': '[ES] Dark Mode',
    'dashboard.language': '[ES] Language',
    'dashboard.logout': '[ES] Logout',
    'dashboard.welcome': '[ES] Welcome,',
    'dashboard.pending': '[ES] Pending',
    'dashboard.inProgress': '[ES] In Progress',
    'dashboard.assessment': '[ES] Assessment',
    'dashboard.agencyAssessmentOfABI': '[ES] Agency Assessment of ABI',
    'dashboard.abiAssessmentOfAgency': '[ES] ABI Assessment of Agency',
    
    // Survey Questions - Placeholders for Spanish translations
    'question.1.name': '[ES] People and Partnership',
    'question.1.text': '[ES] Agency allocates talented teams according to the client\'s requirements...',
    'question.2.name': '[ES] Account Management and Process',
    'question.2.text': '[ES] Agency ensures projects are delivered on time...',
    'question.3.name': '[ES] Strategy',
    'question.3.text': '[ES] Agency understands our business, industry and customers...',
    'question.4.name': '[ES] Creativity',
    'question.4.text': '[ES] Agency delivers ideas on-brief and executable...',
    'question.5.name': '[ES] Media Planning and Execution',
    'question.5.text': '[ES] Agency understands media performance metrics...',
    
    // Survey Interface - Placeholders for Spanish translations
    'survey.question': '[ES] Question',
    'survey.next': '[ES] Next',
    'survey.previous': '[ES] Previous',
    'survey.submit': '[ES] Submit',
    'survey.save': '[ES] Save Progress',
    'survey.comment': '[ES] Comment',
    'survey.rating': '[ES] Rating',
    'survey.progress': '[ES] Progress',
    'survey.loading': '[ES] Loading survey...',
    'survey.submitSurvey': '[ES] Submit Survey',
    'survey.saveAndContinue': '[ES] Save and Continue',
    'survey.alreadyAnswered': '[ES] Already Answered',
    'survey.submitting': '[ES] Submitting...',
    'survey.questionXofY': '[ES] Question {current} of {total}',
    'survey.disagreeCompletely': '[ES] Disagree Completely',
    'survey.agreeCompletely': '[ES] Agree Completely',
    'survey.feedback': '[ES] Feedback',
    'survey.feedbackPrompt': '[ES] Please provide feedback outlining your experience with this agency on the above topic. Your feedback should be specific and actionable.',
    'survey.backToDashboard': '[ES] Back to Dashboard',
    
    // Languages
    'language.english': 'English',
    'language.spanish': 'Español',
    'language.portuguese': 'Português',
  },
  pt: {
    // Dashboard - Placeholders for Portuguese translations
    'dashboard.title': '[PT] Your Surveys',
    'dashboard.surveysCount': '[PT] You have {count} survey{plural} to complete.',
    'dashboard.noSurveys': '[PT] No surveys available at this time.',
    'dashboard.startSurvey': '[PT] Start Survey',
    'dashboard.continueSurvey': '[PT] Continue Survey',
    'dashboard.completed': '[PT] Completed',
    'dashboard.yourPasscode': '[PT] Your Passcode:',
    'dashboard.email': '[PT] Email:',
    'dashboard.darkMode': '[PT] Dark Mode',
    'dashboard.language': '[PT] Language',
    'dashboard.logout': '[PT] Logout',
    'dashboard.welcome': '[PT] Welcome,',
    'dashboard.pending': '[PT] Pending',
    'dashboard.inProgress': '[PT] In Progress',
    'dashboard.assessment': '[PT] Assessment',
    'dashboard.agencyAssessmentOfABI': '[PT] Agency Assessment of ABI',
    'dashboard.abiAssessmentOfAgency': '[PT] ABI Assessment of Agency',
    
    // Survey Questions - Placeholders for Portuguese translations
    'question.1.name': '[PT] People and Partnership',
    'question.1.text': '[PT] Agency allocates talented teams according to the client\'s requirements...',
    'question.2.name': '[PT] Account Management and Process',
    'question.2.text': '[PT] Agency ensures projects are delivered on time...',
    'question.3.name': '[PT] Strategy',
    'question.3.text': '[PT] Agency understands our business, industry and customers...',
    'question.4.name': '[PT] Creativity',
    'question.4.text': '[PT] Agency delivers ideas on-brief and executable...',
    'question.5.name': '[PT] Media Planning and Execution',
    'question.5.text': '[PT] Agency understands media performance metrics...',
    
    // Survey Interface - Placeholders for Portuguese translations
    'survey.question': '[PT] Question',
    'survey.next': '[PT] Next',
    'survey.previous': '[PT] Previous',
    'survey.submit': '[PT] Submit',
    'survey.save': '[PT] Save Progress',
    'survey.comment': '[PT] Comment',
    'survey.rating': '[PT] Rating',
    'survey.progress': '[PT] Progress',
    'survey.loading': '[PT] Loading survey...',
    'survey.submitSurvey': '[PT] Submit Survey',
    'survey.saveAndContinue': '[PT] Save and Continue',
    'survey.alreadyAnswered': '[PT] Already Answered',
    'survey.submitting': '[PT] Submitting...',
    'survey.questionXofY': '[PT] Question {current} of {total}',
    'survey.disagreeCompletely': '[PT] Disagree Completely',
    'survey.agreeCompletely': '[PT] Agree Completely',
    'survey.feedback': '[PT] Feedback',
    'survey.feedbackPrompt': '[PT] Please provide feedback outlining your experience with this agency on the above topic. Your feedback should be specific and actionable.',
    'survey.backToDashboard': '[PT] Back to Dashboard',
    
    // Languages
    'language.english': 'English',
    'language.spanish': 'Español',
    'language.portuguese': 'Português',
  },
};

// Helper function to get translation with interpolation support
export function getTranslation(language: Language, key: string, params?: Record<string, string | number>): string {
  let translation = translations[language][key] || translations.en[key] || key;
  
  if (params) {
    Object.entries(params).forEach(([paramKey, value]) => {
      translation = translation.replace(`{${paramKey}}`, String(value));
    });
  }
  
  return translation;
}
