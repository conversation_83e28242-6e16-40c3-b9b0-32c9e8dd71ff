'use client';

import { Bar<PERSON><PERSON> } from '@mui/x-charts/BarChart';
import { Box, Typography, CircularProgress, useTheme } from '@mui/material';
import { useState, useEffect } from 'react';
import DashboardPanel from './DashboardPanel';
import PanelHeader from './PanelHeader';
import PanelSubheader from './PanelSubheader';

export interface DashboardBarChartProps {
  focusType: 'agency' | 'brand' | 'region' | 'wave' | 'all';
  focusValue: string;
  agency?: string;
  brand?: string;
  region?: string;
  wave?: string;
  height?: number;
}

interface ChartData {
  category: string;
  agencyScore: number;
  abInBevScore: number;
  [key: string]: string | number;
}

interface QuestionAnalytics {
  q1Avg: number;
  q2Avg: number;
  q3Avg: number;
  q4Avg: number;
  q5Avg: number;
  npsAvg: number;
  responseCount: number;
}

export default function DashboardBarChart({
  focusType,
  focusValue,
  agency = 'All Agencies',
  brand = 'All Brands',
  region = 'All Regions',
  wave = 'Latest Wave',
  height = 300,
}: DashboardBarChartProps) {
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [loading, setLoading] = useState(true);
  const [initialLoad, setInitialLoad] = useState(true);
  const theme = useTheme();

  useEffect(() => {
    const fetchChartData = async () => {
      try {
        // Only show loading spinner on initial load, not on subsequent filter changes
        if (initialLoad) {
          setLoading(true);
        }

        // Fetch question analytics for both assessment types
        const params = new URLSearchParams();
        if (agency !== 'All Agencies') params.append('agencyName', agency);
        if (brand !== 'All Brands') params.append('brand', brand);
        if (region !== 'All Regions') params.append('region', region);
        if (wave && wave !== 'Latest Wave') params.append('wave', wave);

        const [agencyOnAbRes, abOnAgencyRes] = await Promise.all([
          fetch(
            `/api/responses/question-analytics?${params.toString()}&assessmentType=Agency-on-Anheuser Busch In-Bev`
          ),
          fetch(
            `/api/responses/question-analytics?${params.toString()}&assessmentType=Anheuser Busch In-Bev-on-Agency`
          ),
        ]);

        const [agencyOnAbData, abOnAgencyData]: [
          QuestionAnalytics,
          QuestionAnalytics
        ] = await Promise.all([agencyOnAbRes.json(), abOnAgencyRes.json()]);

        // Map the 5 questions plus NPS to chart data
        const categories = [
          { key: 'q1Avg', label: 'People & Partnership' },
          { key: 'q2Avg', label: 'Account Management' },
          { key: 'q3Avg', label: 'Strategy' },
          { key: 'q4Avg', label: 'Creativity' },
          { key: 'q5Avg', label: 'Media Planning' },
          // { key: 'npsAvg', label: 'NPS' },
        ];

        const processedData: ChartData[] = categories.map(({ key, label }) => ({
          category: label,
          abInBevScore: abOnAgencyData[key as keyof QuestionAnalytics] || 0,
          agencyScore: agencyOnAbData[key as keyof QuestionAnalytics] || 0,
        }));

        setChartData(processedData);
      } catch (error) {
        console.error('Error fetching chart data:', error);
      } finally {
        if (initialLoad) {
          setLoading(false);
          setInitialLoad(false);
        }
      }
    };

    fetchChartData();
  }, [agency, brand, region, wave, initialLoad]);

  // Only show loading spinner on initial load
  if (loading && initialLoad) {
    return (
      <DashboardPanel
        sx={{
          p: 2,
          height,
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <CircularProgress />
      </DashboardPanel>
    );
  }

  // Filter out categories with no data
  const filteredData = chartData.filter(
    (item) => item.agencyScore > 0 || item.abInBevScore > 0
  );

  if (filteredData.length === 0) {
    return (
      <DashboardPanel
        sx={{
          p: 2,
          height,
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Typography color="text.secondary">
          No data available for the selected filters
        </Typography>
      </DashboardPanel>
    );
  }

  return (
    <DashboardPanel
      sx={{
        paddingX: 1,
        paddingY: 2,
        // width: '50%',
        // background:
        //   'linear-gradient(to right, #f8f9fa 1px, transparent 1px), linear-gradient(to bottom, #f8f9fa 1px, transparent 1px)',
        // backgroundSize: '20px 20px',
        position: 'relative',
        // '&::before': {
        //   content: '""',
        //   position: 'absolute',
        //   top: 0,
        //   left: 0,
        //   right: 0,
        //   bottom: 0,
        //   // background: 'rgba(255, 255, 255, 0.9)',
        //   borderRadius: 2,
        //   zIndex: 0,
        // },
      }}
    >
      <Box sx={{ textAlign: 'center' }}>
        <PanelHeader>{focusValue} Assessment Scores</PanelHeader>
        <PanelSubheader>
          {[
            focusType !== 'agency' && agency !== 'All Agencies' ? agency : null,
            focusType !== 'brand' && brand !== 'All Brands' ? brand : null,
            focusType !== 'region' && region !== 'All Regions' ? region : null,
            focusType !== 'wave' && wave !== 'Latest Wave' ? wave : null,
          ]
            .filter((item) => item !== null)
            .join(' | ') || 'All Filters'}
        </PanelSubheader>
      </Box>
      <Box
        sx={{
          flexGrow: 1,
          minHeight: 0,
          padding: 1,
          position: 'relative',
          zIndex: 1,
        }}
      >
        <BarChart
          dataset={filteredData}
          xAxis={[
            {
              tickLabelInterval: () => true,
              scaleType: 'band',
              dataKey: 'category',
              tickLabelStyle: {
                // angle: -45,
                textAnchor: 'start',
                // position: 'top',
                // fontSize: 12,
              },
            },
          ]}
          yAxis={[
            {
              stroke: '#e0e0e0',
              min: 0,
              max: 5,
              tickNumber: 6,
              tickLabelInterval: () => true,
              tickLabelStyle: {
                // fontSize: 12,
              },
            },
          ]}
          series={[
            {
              dataKey: 'abInBevScore',
              label: 'AB → Agency',
              color: theme.palette.primary.main,
              id: 'abInBevScore',
            },
            {
              dataKey: 'agencyScore',
              label: 'Agency → AB',
              color: theme.palette.secondary.main,
              id: 'agencyScore',
            },
          ]}
          barLabel="value"
          height={height - 40}
          margin={{ left: 0, right: 30, top: 20, bottom: 40 }}
          sx={(theme) => ({
            border: '1px solid rgba(0, 0, 0, 0.05)',
            borderRadius: 2,
            // '& .MuiChartsLegend-root': {
            //   // transform: 'rotate(90deg)',
            // },
            '& .MuiBarElement-root': {
              strokeWidth: 2,
            },
            '& .MuiBarElement-series-abInBevScore': {
              fill: theme.palette.primary.main + '20',
              stroke: theme.palette.primary.main,
            },
            '& .MuiBarElement-series-agencyScore': {
              fill: theme.palette.secondary.main + '20',
              stroke: theme.palette.secondary.main,
            },
            '& .MuiChartsAxis-tick': {
              stroke: '#e0e0e0',
            },
            '& .MuiChartsAxis-line': {
              stroke: '#e0e0e0',
            },
            '& .MuiChartsAxis-bottom .MuiChartsAxis-tickLabel': {
              transform: 'rotate(15deg) translateY(-20px)',
              fill: '#666',
              fontSize: '12px',
            },
          })}
        />
      </Box>
    </DashboardPanel>
  );
}
