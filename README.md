# Echo360 Survey Platform

A modern survey platform built with Next.js and Material-UI.

## Tech Stack

- Next.js 15
- React 19
- Material-UI
- MongoDB
- TypeScript

## Installation

MongoDB:

```bash
brew tap mongodb/brew
brew install mongodb-community
```

Project:

```bash
npm install
```

Download all the .env.\* files (.env.production, .env.test, .env.development, and .env.local) from the team's Google shared DevOps folder and place them in your project's root. If you require local overrides to these variables, you need only to make changes in .env.local.

## Development

First, run the database server:

```bash
brew services start mongodb-community
```

Then, run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Seeding Initial Admin Users

```bash
npm run seed-admins
```

Navigate to /admin/settings in the app to create new admin users

Default accounts created:

- **<EMAIL>** / `SuperAdmin123!` (superadmin)
- **<EMAIL>** / `RegularAdmin123!` (admin)

**⚠️ Change all default passwords immediately after first login!**

## Authentication

**Test the System**:

```bash
npm run test-auth
```

## UI Components

This project uses Material-UI (MUI) as its component library. When creating new components:

1. Use MUI components as the base building blocks
2. Follow the Material Design guidelines
3. Use the theme configuration in `app/_constants/theme.ts` for consistent styling
4. Leverage MUI's built-in styling solutions (sx prop, styled API) for custom styling

## Best practices

For code consistency, have the prettier plugin installed on your IDE.

Before committing any changes, make sure it builds properly with:

```bash
npm run build
```

This will ensure the project will build properly on the production server.
