import fs from 'fs';
import path from 'path';

export function loadEmailTemplate(): string {
  const filePath = path.join(process.cwd(), 'templates', 'email-template.html');
  return fs.readFileSync(filePath, 'utf8');
}

export function processHtmlTemplate(
  html: string,
  data: Record<string, string>
) {
  // Replace placeholders like [FirstName], [PassCode], and [SurveyLink]
  return html.replace(/\[([A-Za-z0-9_]+)]/g, (_, key) => data[key] || '');
}
